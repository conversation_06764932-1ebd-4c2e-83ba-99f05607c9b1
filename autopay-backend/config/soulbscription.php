<?php

return [
    'database' => [
        'cancel_migrations_autoloading' => true,
    ],
    /*
    |--------------------------------------------------------------------------
    | Models Configuration
    |--------------------------------------------------------------------------
    |
    | Here you can configure the models used by the Soulbscription package.
    | You can override the default models with your own custom models.
    |
    */
    'models' => [
        'feature' => \Modules\Core\Models\Feature::class,
        'feature_consumption' => \Modules\Core\Models\FeatureConsumption::class,
        'feature_ticket' => \Modules\Core\Models\FeatureTicket::class,
        'feature_plan' => \Modules\Core\Models\FeaturePlan::class,
        'plan' => \Modules\Core\Models\Plan::class,
        'subscription' => \Modules\Core\Models\Subscription::class,
        'subscription_renewal' => \Modules\Core\Models\SubscriptionRenewal::class,

        'subscriber' => [
            'model' => \Modules\User\Models\User::class,
            'uses_uuid' => false, // Using ULID instead
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Table Names
    |--------------------------------------------------------------------------
    |
    | Here you can configure the table names used by the package.
    |
    */
    'table_names' => [
        'plans' => 'plans',
        'features' => 'features',
        'subscriptions' => 'subscriptions',
        'feature_consumptions' => 'feature_consumptions',
        'feature_tickets' => 'feature_tickets',
        'subscription_renewals' => 'subscription_renewals',
        'feature_plan' => 'feature_plan',
    ],
];
