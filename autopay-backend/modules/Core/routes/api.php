<?php

use Illuminate\Support\Facades\Route;
use Modules\Core\Http\Controllers\FeatureController;
use Modules\Core\Http\Controllers\OrganizationPlanController;
use Modules\Core\Http\Controllers\OrganizationSubscriptionController;

/*
|--------------------------------------------------------------------------
| API Routes for Core Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Core module.
|
*/

// Public access routes
Route::get('/system-plans', [OrganizationPlanController::class, 'systemPlans']);
Route::get('/features', [FeatureController::class, 'index']);

Route::middleware(['auth:sanctum'])->group(function () {

    // Organization-specific routes
    Route::prefix('organizations/{organization}')
        ->middleware(\Modules\Core\Http\Middleware\EnsureOrganizationAccess::class)
        ->group(function () {

        // Organization Plans Management
        Route::prefix('plans')->group(function () {
            Route::get('/', [OrganizationPlanController::class, 'index']); // All available plans
            Route::get('/organization', [OrganizationPlanController::class, 'organizationPlans']); // Org-specific plans
            Route::post('/', [OrganizationPlanController::class, 'store']);
            Route::get('/{plan}', [OrganizationPlanController::class, 'show']);
            Route::put('/{plan}', [OrganizationPlanController::class, 'update']);
            Route::patch('/{plan}/toggle-status', [OrganizationPlanController::class, 'toggleStatus']);
            Route::delete('/{plan}', [OrganizationPlanController::class, 'destroy']);
            Route::get('/stats/usage', [OrganizationPlanController::class, 'usageStats']);
            Route::get('/subscribers', [OrganizationPlanController::class, 'subscribers']);
        });

        // Organization Subscriptions Management
        Route::prefix('subscriptions')->group(function () {
            // Organization-level subscription (to system plans)
            Route::post('/organization', [OrganizationSubscriptionController::class, 'subscribeOrganization']);
            Route::get('/organization', [OrganizationSubscriptionController::class, 'organizationSubscription']);
            Route::get('/organization/usage', [OrganizationSubscriptionController::class, 'organizationUsage']);

            // Member subscriptions (to organization plans)
            Route::post('/users', [OrganizationSubscriptionController::class, 'subscribeUser']);
            Route::get('/users/{user}', [OrganizationSubscriptionController::class, 'userSubscription']);
            Route::delete('/users/{user}', [OrganizationSubscriptionController::class, 'cancelUserSubscription']);
            Route::get('/members', [OrganizationSubscriptionController::class, 'membersWithSubscriptions']);
        });
    });
});
