<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('feature_consumptions', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->decimal('consumption')->unsigned()->nullable();
            $table->timestamp('expired_at')->nullable();
            $table->foreignUlid('feature_id')->constrained('features')->cascadeOnDelete();
            $table->timestamps();

            // Using ULID for subscriber morphs
            $table->ulidMorphs('subscriber');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('feature_consumptions');
    }
};
