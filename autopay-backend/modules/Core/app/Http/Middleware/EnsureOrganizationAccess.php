<?php

namespace Modules\Core\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Organization\Models\Organization;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware to ensure user has access to the organization
 */
class EnsureOrganizationAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $organization = $request->route('organization');

        // Ensure organization exists
        if (!$organization instanceof Organization) {
            return ResponseHelper::error(
                message: 'Organization not found',
                httpCode: 404
            );
        }

        // Ensure user is authenticated
        if (!$user) {
            return ResponseHelper::error(
                message: 'Unauthenticated',
                httpCode: 401
            );
        }

        // Check if user has access to this organization (owner or member)
        if (!$organization->isOwner($user) && !$organization->hasMember($user)) {
            return ResponseHelper::error(
                message: 'Access denied to this organization',
                httpCode: 403
            );
        }

        return $next($request);
    }
}
