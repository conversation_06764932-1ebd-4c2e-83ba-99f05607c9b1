<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;
use Modules\Core\Models\Feature;
use Modules\Core\Models\Plan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            // Add organization_id to distinguish between system plans and organization plans
            $table->foreignUlid('organization_id')
                ->nullable()
                ->after('id')
                ->constrained('organizations')
                ->cascadeOnDelete();

            // Add description field for better plan management
            $table->text('description')->nullable()->after('name');

            // Add indexes for performance
            $table->index(['organization_id']);
            $table->index(['organization_id', 'name']);
            $table->index(['organization_id', 'created_at']);

            // Create unique constraint for plan names within same organization
            // System plans (organization_id = null) can have same names as org plans
            $table->unique(['organization_id', 'name'], 'plans_org_name_unique');
        });



         $transactions = Feature::create([
            'id' => \Illuminate\Support\Str::ulid(),
            'name' => 'transactions',
            'consumable' => true,
            'postpaid' => true,
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
        ]);

        $free = Plan::create([
            'id' => \Illuminate\Support\Str::ulid(),
            'organization_id' => null, // System plan
            'name' => 'freemium',
            'description' => 'Gói dịch vụ miễn phí với số lượng giao dịch giới hạn cho mục đích thử nghiệm',
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
        ]);

        $free->features()->attach($transactions, [
            'charges' => 50, // 50 transactions per month
        ]);

        $starter = Plan::create([
            'id' => \Illuminate\Support\Str::ulid(),
            'organization_id' => null, // System plan
            'name' => 'pay-as-you-go',
            'description' => 'Pay-as-you-go, gói dịch vụ thanh toán theo sử dụng thực tế',
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
            'grace_days' => 1,
        ]);

        $starter->features()->attach($transactions, [
            'charges' => 0, // Unlimited transactions (postpaid billing)
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->dropUnique('plans_org_name_unique');
            $table->dropIndex(['organization_id', 'created_at']);
            $table->dropIndex(['organization_id', 'name']);
            $table->dropIndex(['organization_id']);
            $table->dropForeign(['organization_id']);
            $table->dropColumn(['organization_id', 'description']);
        });
    }
};
