<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;
use Modules\Core\Models\Feature;
use Modules\Core\Models\Plan;

class CoreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create features
        $transactions = new Feature([
            'name' => 'transactions',
            'consumable' => true,
            'postpaid' => true,
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
        ]);
        $transactions->save();

        // Create system plans
        $free = new Plan([
            'organization_id' => null, // System plan
            'name' => 'freemium',
            'description' => 'Gói dịch vụ miễn phí với số lượng giao dịch giới hạn cho mục đích thử nghiệm',
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
        ]);
        $free->save();

        $free->features()->attach($transactions, [
            'charges' => 50, // 50 transactions per month
        ]);

        $starter = new Plan([
            'organization_id' => null, // System plan
            'name' => 'pay-as-you-go',
            'description' => 'Pay-as-you-go, gói dịch vụ thanh toán theo sử dụng thực tế',
            'periodicity_type' => PeriodicityType::Month,
            'periodicity' => 1,
            'grace_days' => 1,
        ]);
        $starter->save();

        $starter->features()->attach($transactions, [
            'charges' => 0, // Unlimited transactions (postpaid billing)
        ]);
    }
}
