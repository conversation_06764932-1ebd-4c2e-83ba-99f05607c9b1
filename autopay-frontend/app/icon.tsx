import { getDomainConfig } from '@/lib/server/domain'
import { readFile } from 'fs/promises'
import { ImageResponse } from 'next/og'
import { join } from 'path'

// Image metadata
export const size = {
  width: 32,
  height: 32,
}
export const contentType = 'image/png'

// Image generation
export default async function Icon(): Promise<ImageResponse> {
  const domainConfig = await getDomainConfig()

  // If this is a mapped domain (has domain config), use domain data
  if (domainConfig) {
    const brandName = domainConfig.branding?.name || 'AutoPAY'
    const faviconUrl = domainConfig.branding?.favicon_url

    // If favicon URL is available, try to use it
    if (faviconUrl) {
      try {
        // Fetch the favicon image
        const faviconResponse = await fetch(faviconUrl)
        if (faviconResponse.ok) {
          const faviconBuffer = await faviconResponse.arrayBuffer()
          const faviconBase64 = Buffer.from(faviconBuffer).toString('base64')
          const faviconMimeType = faviconResponse.headers.get('content-type') || 'image/png'

          return new ImageResponse(
            (
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'white',
                }}>
                <img
                  src={`data:${faviconMimeType};base64,${faviconBase64}`}
                  alt={brandName}
                  style={{
                    width: '20px',
                    height: '20px',
                    objectFit: 'contain',
                  }}
                />
              </div>
            ),
            {
              ...size,
            }
          )
        }
      } catch (error) {
        // Fall through to text-based icon
      }
    }

    // Fallback to text-based icon using first letter of brand name for mapped domains
    const firstLetter = brandName.charAt(0).toUpperCase()

    return new ImageResponse(
      (
        <div
          style={{
            fontSize: 20,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
            fontFamily: 'system-ui, sans-serif',
          }}>
          {firstLetter}
        </div>
      ),
      {
        ...size,
      }
    )
  }

  // Default domain (APP_URL) - use default logo
  try {
    const logoPath = join(process.cwd(), 'assets', 'images', 'logo-icon.png')
    const logoBuffer = await readFile(logoPath)
    const logoBase64 = logoBuffer.toString('base64')

    return new ImageResponse(
      (
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'blue',
          }}>
          <img
            src={`data:image/png;base64,${logoBase64}`}
            alt="AutoPAY"
            style={{
              width: '25px',
              height: '25px',
              objectFit: 'contain',
              objectPosition: 'center center',
            }}
          />
        </div>
      ),
      {
        ...size,
      }
    )
  } catch (error) {
    // Final fallback to text-based icon with "A" for AutoPAY
    return new ImageResponse(
      (
        <div
          style={{
            fontSize: 20,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
            fontFamily: 'system-ui, sans-serif',
          }}>
          A
        </div>
      ),
      {
        ...size,
      }
    )
  }
}
