'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Loader2, Plus, Trash2 } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import * as z from 'zod'

const formSchema = z.object({
  name: z.string().min(1, 'Tên gói dịch vụ là bắt buộc'),
  description: z.string().optional(),
  grace_days: z.number().min(0).max(365).default(0),
  periodicity: z.number().min(1).default(1),
  periodicity_type: z.enum(['day', 'week', 'month', 'year']).default('month'),
})

type FormData = z.infer<typeof formSchema>

interface Feature {
  id: string
  name: string
  consumable: boolean
  quota: boolean
  postpaid: boolean
}

interface FeaturesResponse {
  success: boolean
  code?: number
  locale?: string
  message?: string
  data: {
    items: Feature[]
  }
}

interface CreatePlanModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export default function CreatePlanModal({ open, onOpenChange, onSuccess }: CreatePlanModalProps) {
  const { user } = useUser()
  const queryClient = useQueryClient()
  const [selectedFeatures, setSelectedFeatures] = useState<Array<{ feature_id: string; charges: number | null }>>([])

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      grace_days: 0,
      periodicity: 1,
      periodicity_type: 'month',
    },
  })

  // Fetch available features
  const { data: featuresData, isLoading: featuresLoading } = useQuery<FeaturesResponse>({
    queryKey: ['getFeatures'],
    queryFn: () => queryFetchHelper('/features'),
    enabled: open,
  })

  // Create plan mutation
  const createPlanMutation = useMutation({
    mutationFn: async (data: FormData) => {
      return queryFetchHelper(`/organizations/${user?.current_organization?.id}/plans`, {
        method: 'POST',
        body: JSON.stringify({
          ...data,
          features: selectedFeatures.map((feature) => ({
            feature_id: String(feature.feature_id), // Ensure feature_id is string
            charges: feature.charges,
          })),
        }),
      })
    },
    onSuccess: () => {
      toast.success('Tạo gói dịch vụ thành công!')
      queryClient.invalidateQueries({ queryKey: ['getOrganizationPlans'] })
      onSuccess?.()
      onOpenChange(false)
      form.reset()
      setSelectedFeatures([])
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi tạo gói dịch vụ')
    },
  })

  const onSubmit = (data: FormData) => {
    createPlanMutation.mutate(data)
  }

  const addFeature = (featureId: string) => {
    if (!selectedFeatures.find((f) => f.feature_id === featureId)) {
      setSelectedFeatures([...selectedFeatures, { feature_id: featureId, charges: null }])
    }
  }

  const updateFeatureCharges = (featureId: string, charges: number | null) => {
    setSelectedFeatures((prev) => prev.map((f) => (f.feature_id === featureId ? { ...f, charges } : f)))
  }

  const removeFeature = (featureId: string) => {
    setSelectedFeatures((prev) => prev.filter((f) => f.feature_id !== featureId))
  }

  const availableFeatures =
    featuresData?.data?.items?.filter((feature) => !selectedFeatures.find((sf) => sf.feature_id === feature.id)) || []

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Tạo gói dịch vụ mới</DialogTitle>
          <DialogDescription>Tạo gói dịch vụ tùy chỉnh cho thành viên trong tổ chức</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên gói dịch vụ</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ví dụ: Gói Cơ bản"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mô tả</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Mô tả chi tiết về gói dịch vụ..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="periodicity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Chu kỳ</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="periodicity_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Đơn vị thời gian</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Chọn đơn vị" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="day">Ngày</SelectItem>
                          <SelectItem value="week">Tuần</SelectItem>
                          <SelectItem value="month">Tháng</SelectItem>
                          <SelectItem value="year">Năm</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="grace_days"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Thời gian gia hạn (ngày)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="365"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>Số ngày gia hạn sau khi gói hết hạn</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Features Management */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Tính năng</h3>
                  <p className="text-muted-foreground text-sm">Chọn các tính năng có trong gói dịch vụ</p>
                </div>
              </div>

              {/* Add Feature */}
              {availableFeatures.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Thêm tính năng</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {availableFeatures.map((feature) => (
                        <Button
                          key={feature.id}
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addFeature(feature.id)}
                          className="h-auto p-2">
                          <Plus className="mr-1 h-3 w-3" />
                          {feature.name}
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Selected Features */}
              {selectedFeatures.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Tính năng đã chọn</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedFeatures.map((selectedFeature) => {
                        const feature = featuresData?.data?.items?.find((f) => f.id === selectedFeature.feature_id)
                        if (!feature) return null

                        return (
                          <div
                            key={selectedFeature.feature_id}
                            className="flex items-center justify-between rounded border p-3">
                            <div className="flex items-center space-x-3">
                              <Badge variant="secondary">{feature.name}</Badge>
                              <div className="text-muted-foreground text-xs">
                                {feature.consumable ? 'Tiêu thụ' : 'Không tiêu thụ'} •{' '}
                                {feature.postpaid ? 'Trả sau' : 'Trả trước'}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="flex items-center space-x-2">
                                <Input
                                  type="number"
                                  placeholder="Không giới hạn"
                                  value={selectedFeature.charges || ''}
                                  onChange={(e) =>
                                    updateFeatureCharges(
                                      selectedFeature.feature_id,
                                      e.target.value ? parseInt(e.target.value) : null
                                    )
                                  }
                                  className="w-32"
                                />
                                <span className="text-muted-foreground text-sm">lần</span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFeature(selectedFeature.feature_id)}>
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}

              {featuresLoading && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span className="text-muted-foreground text-sm">Đang tải tính năng...</span>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}>
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={createPlanMutation.isPending}>
                {createPlanMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Tạo gói dịch vụ
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
