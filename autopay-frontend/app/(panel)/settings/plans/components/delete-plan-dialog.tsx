'use client'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { Loader2 } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import type { OrganizationPlan } from '../page'

interface DeletePlanDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  plan: OrganizationPlan | null
  onSuccess?: () => void
}

export default function DeletePlanDialog({ open, onOpenChange, plan, onSuccess }: DeletePlanDialogProps) {
  const { user } = useUser()
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async (): Promise<void> => {
    if (!plan || !user?.current_organization?.id) return

    setIsDeleting(true)
    try {
      const response = await queryFetchHelper(`/organizations/${user.current_organization.id}/plans/${plan.id}`, {
        method: 'DELETE',
      })

      if (response.success) {
        toast.success('Đã xóa gói dịch vụ thành công')
        onOpenChange(false)
        onSuccess?.()
      } else {
        toast.error(response.message || 'Có lỗi xảy ra')
      }
    } catch (error) {
      toast.error('Không thể xóa gói dịch vụ')
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <AlertDialog
      open={open}
      onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa gói dịch vụ</AlertDialogTitle>
          <AlertDialogDescription>
            Bạn có chắc chắn muốn xóa gói dịch vụ <strong>&ldquo;{plan?.name}&rdquo;</strong>?
            <br />
            <br />
            Hành động này không thể hoàn tác và sẽ ảnh hưởng đến{' '}
            <strong>{plan?.subscriptions?.length || 0} thành viên</strong> đang sử dụng gói này.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Hủy</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive hover:bg-destructive/90 text-white">
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xóa gói dịch vụ
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
