'use client'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'

import globalConfig from '@/lib/config'
import Avatar from 'boring-avatars'
import { AlertCircle } from 'lucide-react'
import { useParams, usePathname, useRouter } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'
import { GiFlyingFlag } from 'react-icons/gi'
import { HiArrowPath, HiChevronRight, HiChevronUpDown, HiPlusCircle, HiXMark } from 'react-icons/hi2'
import { IoCheckmarkCircle } from 'react-icons/io5'
import { toast } from 'sonner'
import { useDebounceValue } from 'usehooks-ts'

// Import Zustand store hooks
import { useOrganizations } from '@/lib/hooks/useOrganizations'
import {
  getDefaultOrganization,
  useHasHydrated,
  useIsDropdownOpen,
  useOrganizationsError,
  useOrganizations as useOrganizationsFromStore,
  useOrganizationsLoading,
  useSearchQuery,
  useSelectedOrganization,
  useSelectedTeam,
  useSetDropdownOpen,
  useSetSearchQuery,
  useSetSelectedOrganization,
  useSetSelectedTeam,
  useSetShowTeams,
  useSetTeamSearchQuery,
  useShowTeams,
  useTeamSearchQuery,
} from '@/lib/stores/organizationStore'

// Import Create Team modal
import AddTeamModal from '@/app/(panel)/members/teams/components/modals/add-team'
import NiceModal from '@ebay/nice-modal-react'

const avatarColors = ['#aef055', '#e0ffc3', '#25e4bc', '#3f8978', '#514442']

// Global flag to prevent URL changes when user is selecting organization
let isUserManuallySelectingOrg = false

// Helper function to check if organization has teams
const hasTeams = (org: Organization | null): boolean => {
  return !!(org?.teams && org.teams.length > 0)
}

// Helper function to create organization URL
const createOrgUrl = (org: Organization): string => {
  return '/' + (org.alias || org.id)
}

// Organization-level pages that should not be treated as team routes
const ORG_LEVEL_PAGES = globalConfig.orgLevelPages

// Helper function to check if a path segment is an organization-level page
const isOrgLevelPage = (pathSegment: string): boolean => {
  return ORG_LEVEL_PAGES.includes(pathSegment)
}

// Helper function to parse URL and extract org/team aliases
const parseUrlParams = (pathname: string): { orgAlias?: string; teamAlias?: string; isOrgLevel?: boolean } => {
  const pathParts = pathname.split('/').filter(Boolean)

  // URL format: /org/team/...
  if (pathParts.length >= 2) {
    const potentialTeam = pathParts[1]

    // Check if second segment is an org-level page
    if (isOrgLevelPage(potentialTeam)) {
      return {
        orgAlias: pathParts[0],
        isOrgLevel: true,
      }
    }

    return {
      orgAlias: pathParts[0],
      teamAlias: pathParts[1],
      isOrgLevel: false,
    }
  }

  // URL format: /org
  if (pathParts.length === 1) {
    return {
      orgAlias: pathParts[0],
      isOrgLevel: false,
    }
  }

  return { isOrgLevel: false }
}

export default function OrganizationSwitch() {
  const router = useRouter()
  const params = useParams()
  const pathname = usePathname()
  const teamAliasFromUrl = params?.team as string | undefined

  // Parse URL to get org and team aliases
  const { orgAlias, teamAlias, isOrgLevel } = parseUrlParams(pathname)

  // Get data and state from Zustand store
  const selectedOrganization = useSelectedOrganization()
  const selectedTeam = useSelectedTeam()
  const organizations = useOrganizationsFromStore()
  const isLoading = useOrganizationsLoading()
  const isError = useOrganizationsError()
  const hasHydrated = useHasHydrated()

  // Get store actions
  const setSelectedOrganization = useSetSelectedOrganization()
  const setSelectedTeam = useSetSelectedTeam()
  const searchQuery = useSearchQuery()
  const setSearchQuery = useSetSearchQuery()
  const teamSearchQuery = useTeamSearchQuery()
  const setTeamSearchQuery = useSetTeamSearchQuery()
  const open = useIsDropdownOpen()
  const setOpen = useSetDropdownOpen()
  const showTeams = useShowTeams()
  const setShowTeams = useSetShowTeams()

  // Local state for previous organization
  const [previousOrganization, setPreviousOrganization] = useState<Organization | null>(null)

  // Debounced search queries
  const [debouncedSearchQuery] = useDebounceValue(searchQuery, 800)
  const [debouncedTeamSearchQuery] = useDebounceValue(teamSearchQuery, 300)

  // Fetch organizations data and sync with store
  const { refetch } = useOrganizations()

  // Get teams from store computed function
  const teams = useMemo(() => {
    if (!selectedOrganization?.teams) {
      return []
    }

    // Filter teams based on search query
    if (!debouncedTeamSearchQuery) {
      return selectedOrganization.teams
    }

    return selectedOrganization.teams.filter(
      (team) =>
        team.name.toLowerCase().includes(debouncedTeamSearchQuery.toLowerCase()) ||
        (team.alias && team.alias.toLowerCase().includes(debouncedTeamSearchQuery.toLowerCase()))
    )
  }, [selectedOrganization?.teams, debouncedTeamSearchQuery])

  // Hide the teams section when actively searching (but not when the organization is yet selected)
  useEffect(() => {
    // Only hide teams if searching and no organization is selected yet
    if (searchQuery && !selectedOrganization) {
      setShowTeams(false)
    }
  }, [searchQuery, selectedOrganization, setShowTeams])

  // Sync organization and team from URL when data loads
  useEffect(() => {
    if (searchQuery) {
      return
    }

    // Don't do anything if still loading or not hydrated
    if (isLoading || !hasHydrated) {
      return
    }

    // Don't interfere if user is manually selecting organization
    if (isUserManuallySelectingOrg) {
      return
    }

    if (organizations.length === 0) {
      return
    }

    // Handle direct URL access like /org/team or /org/settings
    if (orgAlias && (teamAlias || isOrgLevel)) {
      const orgFromUrl = organizations.find((org) => org.alias === orgAlias || org.id === orgAlias)

      if (orgFromUrl) {
        // Set organization regardless of whether it's org-level or team-level
        setSelectedOrganization(orgFromUrl)

        // If it's an org-level page (like /org/settings), don't try to find a team
        if (isOrgLevel) {
          setSelectedTeam(null)
          return
        }

        // Handle team-level URLs
        if (teamAlias) {
          // Organization exists, check if team exists
          if (orgFromUrl.teams && orgFromUrl.teams.length > 0) {
            const teamFromUrl = orgFromUrl.teams.find((team) => team.alias === teamAlias || team.id === teamAlias)

            if (teamFromUrl) {
              // Both org and team are valid - just sync state, don't redirect
              setSelectedTeam(teamFromUrl)
              return
            }
          }
        }
      }
    }

    // If no org in URL and no org selected from persist, select the default org (but don't redirect)
    if (!selectedOrganization && organizations.length > 0) {
      const defaultOrg = getDefaultOrganization(organizations)
      if (defaultOrg) {
        setSelectedOrganization(defaultOrg)
        // Don't automatically redirect - let user choose where to go
      }
    }
  }, [
    organizations,
    searchQuery,
    teamAliasFromUrl,
    orgAlias,
    teamAlias,
    isOrgLevel,
    selectedOrganization,
    setSelectedOrganization,
    setSelectedTeam,
    isLoading,
    hasHydrated,
  ])

  // Sync team from URL when teams data loads
  useEffect(() => {
    if (!selectedOrganization || !selectedOrganization.teams || teams.length === 0) {
      return
    }

    // Skip if we're on an org-level page (like /org/settings)
    if (isOrgLevel) {
      return
    }

    // Skip if we already handled team selection in the main useEffect for direct URL access
    if (orgAlias && teamAlias && selectedTeam) {
      return
    }

    // Try to find team from URL first
    if (teamAliasFromUrl) {
      const teamFromUrl = teams.find((team) => team.alias === teamAliasFromUrl || team.id === teamAliasFromUrl)
      if (teamFromUrl) {
        setSelectedTeam(teamFromUrl)
        return
      }
    }

    // Try to find team from parsed URL
    if (teamAlias) {
      const teamFromUrl = teams.find((team) => team.alias === teamAlias || team.id === teamAlias)
      if (teamFromUrl) {
        setSelectedTeam(teamFromUrl)
        return
      }
    }

    // Don't automatically select any team - let user choose
  }, [teams, teamAliasFromUrl, teamAlias, orgAlias, isOrgLevel, setSelectedTeam, selectedOrganization, selectedTeam])

  const handleSelectOrg = (org: Organization) => {
    // Set flag to prevent useEffect interference
    isUserManuallySelectingOrg = true

    // Save current organization before changing (for potential restore)
    setPreviousOrganization(selectedOrganization)

    // Update the selected organization
    setSelectedOrganization(org)

    // Check if organization has teams
    if (hasTeams(org)) {
      // Show teams section and keep dropdown open for user to select specific team
      setShowTeams(true)
      // Clear any previously selected team since user is choosing a new org
      setSelectedTeam(null)
      // Don't close dropdown - let user choose team
      // Don't navigate anywhere - wait for user to select a team
    } else {
      // No teams available, just navigate to organization and close dropdown
      setShowTeams(false)
      setSelectedTeam(null)
      setOpen(false)

      // Navigate to organization URL only if no teams
      router.push(createOrgUrl(org))

      // Reset flag after navigation
      setTimeout(() => (isUserManuallySelectingOrg = false), 500)
    }
  }

  const handleSelectTeam = (team: Team) => {
    // Reset flag
    isUserManuallySelectingOrg = false

    // Clear previous organization since user confirmed selection
    setPreviousOrganization(null)

    // Update the selected team
    setSelectedTeam(team)
    toast.success('Đã chuyển sang ' + team.name)

    // Close dropdown
    setOpen(false)
    setShowTeams(false)

    // Don't navigate - just update the selected team state
  }

  const handleTeamNameClick = () => {
    if (!selectedTeam || !selectedOrganization) {
      return
    }

    // Always open dropdown to allow organization/team switching
    setOpen(true)

    // Show teams section if the organization has teams
    if (hasTeams(selectedOrganization)) {
      setShowTeams(true)
    }
  }

  // Show loading state while hydrating
  if (!hasHydrated) {
    return (
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          disabled
          className="border-muted-foreground/50 dark:border-accent text-muted-foreground w-40 justify-between bg-transparent">
          <div className="flex w-full items-center gap-2">
            <Skeleton className="size-5 rounded-full" />
            <Skeleton className="h-4 flex-1" />
          </div>
          <HiChevronUpDown className="ml-2 size-4 shrink-0 opacity-50" />
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="flex items-center gap-2">
        <Popover
          open={open}
          onOpenChange={(isOpen) => {
            setOpen(isOpen)

            if (isOpen && hasTeams(selectedOrganization)) {
              setShowTeams(true)
            } else {
              setShowTeams(false)
              // Reset flag when dropdown closes
              isUserManuallySelectingOrg = false

              // Only restore previous organization if user was in the middle of selecting
              // and didn't complete the selection (i.e., they selected an org but didn't pick a team)
              if (previousOrganization && isUserManuallySelectingOrg) {
                setSelectedOrganization(previousOrganization)
                setPreviousOrganization(null)
                toast.info('Đã hoàn tác về ' + previousOrganization.name)
              } else {
                // Clear previous organization since user either completed selection or cancelled
                setPreviousOrganization(null)
              }
            }
          }}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="border-muted-foreground/50 dark:border-accent text-muted-foreground w-40 justify-between bg-transparent hover:bg-transparent">
              {isLoading ? (
                <div className="flex w-full items-center gap-2">
                  <Skeleton className="size-5 rounded-full" />
                  <Skeleton className="h-4 flex-1" />
                </div>
              ) : isError ? (
                <div className="text-destructive flex items-center gap-2">
                  <AlertCircle className="size-4" />
                  <span>Lỗi</span>
                </div>
              ) : selectedOrganization ? (
                <div className="flex items-center gap-2">
                  <Avatar
                    name={(selectedOrganization.name ?? selectedOrganization.alias).toUpperCase()}
                    colors={avatarColors}
                    variant="ring"
                    size={20}
                  />
                  <span className="max-w-20 truncate">{selectedOrganization.name ?? selectedOrganization.alias}</span>
                </div>
              ) : (
                'Tổ chức...'
              )}
              <HiChevronUpDown className="ml-2 size-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className={`p-0 ${showTeams && hasTeams(selectedOrganization) && organizations.length > 0 ? 'w-[560px]' : 'w-[280px]'}`}
            align="start"
            side="bottom">
            <div
              className={`flex ${showTeams && hasTeams(selectedOrganization) && organizations.length > 0 ? 'divide-x' : ''}`}>
              {/* Organizations Section */}
              <div
                className={`${showTeams && hasTeams(selectedOrganization) && organizations.length > 0 ? 'w-1/2' : 'w-full'}`}>
                <Command>
                  <div className="relative">
                    <CommandInput
                      placeholder="Tìm kiếm tổ chức..."
                      value={searchQuery}
                      onValueChange={(value) => setSearchQuery(value)}
                    />
                    {searchQuery && (
                      <Button
                        variant="ghost"
                        size="sm"
                        data-tooltip-html="Xóa tìm kiếm"
                        onClick={() => setSearchQuery('')}
                        className="hover:bg-muted absolute top-1/2 right-2 -translate-y-1/2 rounded-sm p-1 transition-colors"
                        type="button">
                        <HiXMark className="text-muted-foreground hover:text-foreground size-3" />
                      </Button>
                    )}
                  </div>
                  <CommandList>
                    {(isLoading || searchQuery !== debouncedSearchQuery) && (
                      <div className="text-muted-foreground py-6 text-center text-sm">
                        {searchQuery ? 'Đang tìm kiếm tổ chức...' : 'Đang tải tổ chức...'}
                      </div>
                    )}
                    {isError && (
                      <div className="text-destructive flex flex-col items-center justify-center gap-2 py-4 text-center text-sm">
                        <AlertCircle className="size-5" />
                        <p>Không thể tải dữ liệu</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-2"
                          onClick={() => refetch()}>
                          <HiArrowPath className="size-4" />
                          Thử lại
                        </Button>
                      </div>
                    )}
                    <CommandEmpty className="py-0">
                      <div className="space-y-2 py-2">
                        <div className="text-muted-foreground px-2 text-center">
                          Không tìm thấy tổ chức nào. Tạo một nhóm để bắt đầu.
                        </div>
                        <Separator />
                        <div className="px-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              setOpen(false)
                              NiceModal.show(AddTeamModal, {
                                callback: () => {
                                  // Refetch organizations to get updated teams
                                  window.location.reload()
                                },
                              })
                            }}
                            className="flex w-full items-center justify-start gap-2">
                            <HiPlusCircle className="size-4" />
                            Tạo Nhóm
                          </Button>
                        </div>
                      </div>
                    </CommandEmpty>
                    {organizations.length > 0 && (
                      <>
                        <CommandGroup>
                          {organizations.map((org) => (
                            <CommandItem
                              key={org.id}
                              value={org.alias}
                              onSelect={() => handleSelectOrg(org)}
                              className="flex items-center gap-2">
                              <Avatar
                                name={org.name ?? org.alias}
                                colors={avatarColors}
                                variant="ring"
                                size={20}
                              />
                              {org.name ?? org.alias}
                              {org.is_default_organization && <GiFlyingFlag className="text-foreground size-3" />}
                              {selectedOrganization?.id === org.id && (
                                <IoCheckmarkCircle className="text-primary ml-auto size-4" />
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </>
                    )}

                    {/* Show a message when searching for organizations */}
                    {searchQuery && !isLoading && (
                      <div className="border-t px-3 py-2">
                        <div className="text-muted-foreground text-center text-xs">
                          {selectedOrganization
                            ? 'Xóa tìm kiếm để xem tất cả tổ chức'
                            : 'Chọn một tổ chức để xem các nhóm'}
                        </div>
                      </div>
                    )}

                    {/* Show selected team info when not searching and a team is selected */}
                    {!searchQuery && selectedTeam && (
                      <div className="border-t px-3 py-2">
                        <div className="text-muted-foreground mb-1 text-center text-xs">Nhóm Hiện Tại:</div>
                        <div className="flex items-center justify-center gap-2">
                          <Avatar
                            name={selectedTeam.name ?? selectedTeam.alias}
                            colors={avatarColors}
                            variant="beam"
                            size={16}
                          />
                          <span className="text-foreground text-sm font-medium">
                            {selectedTeam.name ?? selectedTeam.alias}
                          </span>
                        </div>
                      </div>
                    )}
                  </CommandList>
                </Command>
              </div>

              {/* Teams Section - Only show when an organization is selected */}
              {showTeams && hasTeams(selectedOrganization) && organizations.length > 0 && (
                <div className="w-1/2">
                  <Command>
                    <div className="relative">
                      <CommandInput
                        placeholder="Tìm kiếm nhóm..."
                        value={teamSearchQuery}
                        onValueChange={(value) => setTeamSearchQuery(value)}
                      />
                      {teamSearchQuery && (
                        <Button
                          variant="ghost"
                          size="sm"
                          data-tooltip-html="Xóa tìm kiếm"
                          onClick={() => setTeamSearchQuery('')}
                          className="hover:bg-muted absolute top-1/2 right-2 -translate-y-1/2 rounded-sm p-1 transition-colors"
                          type="button">
                          <HiXMark className="text-muted-foreground hover:text-foreground size-3" />
                        </Button>
                      )}
                    </div>
                    <CommandList>
                      {/* Show teams if available */}
                      {teams.length > 0 ? (
                        <CommandGroup>
                          {teams.map((team) => (
                            <CommandItem
                              key={team.id}
                              value={team.name}
                              onSelect={() => handleSelectTeam(team)}
                              className="flex items-center gap-2">
                              <Avatar
                                name={team.name ?? team.alias}
                                colors={avatarColors}
                                variant="beam"
                                size={20}
                              />
                              {team.name ?? team.alias}
                              {selectedTeam?.id === team.id && (
                                <IoCheckmarkCircle className="text-primary ml-auto size-4" />
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      ) : (
                        <CommandEmpty className="px-2 py-0">
                          <div className="space-y-2 py-2">
                            <div className="text-muted-foreground text-center text-sm">Không tìm thấy nhóm nào.</div>
                          </div>
                        </CommandEmpty>
                      )}
                    </CommandList>

                    {/* Always show Create Team button - Outside CommandList */}
                    <div className="border-y p-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          setOpen(false)
                          setShowTeams(false)
                          NiceModal.show(AddTeamModal, {
                            callback: () => {
                              // Refetch organizations to get updated teams
                              window.location.reload()
                            },
                          })
                        }}
                        className="flex w-full items-center justify-start gap-2">
                        <HiPlusCircle className="size-4" />
                        Tạo Nhóm
                      </Button>
                    </div>
                  </Command>
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>

        {/* Breadcrumb separator */}
        {selectedTeam && <HiChevronRight className="text-muted-foreground size-3" />}

        {/* Display the selected team next to the organization dropdown */}
        {selectedTeam && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleTeamNameClick}
            className="border-muted-foreground/50 dark:border-accent text-muted-foreground flex h-auto w-fit cursor-pointer items-center justify-start gap-2 rounded-md border bg-transparent px-2 py-1 transition-colors hover:bg-transparent">
            <Avatar
              name={selectedTeam.name ?? selectedTeam.alias}
              colors={avatarColors}
              variant="beam"
              size={16}
            />
            <span className="text-sm">{selectedTeam.name ?? selectedTeam.alias}</span>
          </Button>
        )}
      </div>
    </>
  )
}
