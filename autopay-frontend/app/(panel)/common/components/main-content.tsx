'use client'

import Notifications from '@/app/(panel)/common/components/notifications'
import OrganizationSwitch from '@/app/(panel)/common/components/organization-switch'
import UserNav from '@/app/(panel)/common/components/user-nav'
import NextBreadcrumb from '@/components/display/NextBreadcrumb'
import { useIsMobile } from '@/hooks/use-mobile'
import { cn } from '@/lib/utils'
import { useStore } from '@/stores/store'
import { PanelLeft } from 'lucide-react'

export default function Component({ children }: { children: React.ReactNode }) {
  const { setShowLeftNavigationOnMobile } = useStore()
  const isMobile = useIsMobile()

  return (
    <div className="flex w-full flex-col gap-4 overflow-hidden">
      <header
        className={cn(
          'bg-background fixed top-0 right-0 left-(--sidebar-width) z-30 flex h-14 items-center gap-4 border-b p-4',
          {
            'left-0': isMobile,
          }
        )}>
        <PanelLeft
          className="h-5 w-5 cursor-pointer sm:hidden"
          onClick={() => setShowLeftNavigationOnMobile(true)}
        />
        <OrganizationSwitch />
        <Notifications />
        <UserNav />
      </header>
      <main className="flex-1 items-start space-y-4 p-4 sm:py-0 md:space-y-8">
        <NextBreadcrumb />
        {children}
      </main>
    </div>
  )
}
